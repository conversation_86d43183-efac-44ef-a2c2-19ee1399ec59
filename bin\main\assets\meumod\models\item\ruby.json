{
  "_comment": "=============================================================================",
  "_comment2": "MODELO 3D DO ITEM RUBY",
  "_comment3": "=============================================================================",
  "_comment4": "Este arquivo define como o item Ruby aparece visualmente no jogo:",
  "_comment5": "- No inventário do jogador",
  "_comment6": "- Quando segurado na mão",
  "_comment7": "- Quando dropado no chão",
  "_comment8": "- Em interfaces de crafting, etc.",
  "_comment9": "",

  "_comment10": "MODELO PAI (BASE)",
  "_comment11": "Define qual modelo 3D usar como base",
  "_comment12": "item/generated = modelo padrão para itens simples (2D no inventário)",
  "_comment13": "Outras opções: item/handheld (para ferramentas), item/bow, etc.",
  "parent": "item/generated",

  "_comment14": "TEXTURAS DO MODELO",
  "_comment15": "Define quais arquivos de textura aplicar ao modelo",
  "textures": {
    "_comment16": "CAMADA PRINCIPAL DA TEXTURA",
    "_comment17": "layer0 = primeira camada (a principal)",
    "_comment18": "Formato do caminho: [modid]:item/[nome_do_arquivo_sem_extensão]",
    "_comment19": "Aponta para: assets/meumod/textures/item/ruby.png",
    "layer0": "meumod:item/ruby"

    "_comment20": "OUTRAS CAMADAS POSSÍVEIS:",
    "_comment21": "layer1, layer2, etc. = camadas adicionais (sobreposições)",
    "_comment22": "Útil para itens com múltiplas partes visuais"
  }
}
